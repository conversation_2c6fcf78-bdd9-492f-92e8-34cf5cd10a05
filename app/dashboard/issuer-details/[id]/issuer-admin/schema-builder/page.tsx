'use client';

import { ItemBox } from '@/components/ItemBox';

const IssuerAdmin = () => {
    return (
        <div className="flex flex-row items-center gap-4 justify-center h-full px-4 md:px-8">
            <div className="flex-1 flex flex-col h-full gap-4">
                <ItemBox>
                    <div className="p-4 flex flex-1 flex-col gap-4">Details of issuer</div>
                </ItemBox>
                <ItemBox>
                    <div className="p-4 flex flex-[2] flex-col gap-4">Schema Version</div>
                </ItemBox>
            </div>
            <div className="flex-1 flex flex-col h-full gap-4">
                <ItemBox>
                    <div className="p-4 flex flex-col gap-4">Schema display</div>
                </ItemBox>
                <ItemBox>
                    <div className="p-4 flex flex-col gap-4">json view</div>
                </ItemBox>
            </div>
        </div>
    );
};

export default IssuerAdmin;
