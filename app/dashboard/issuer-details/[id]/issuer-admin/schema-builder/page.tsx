'use client';

import { ItemBox } from '@/components/ItemBox';
import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { TextInput } from '@/components/FormFields';
import { ButtonGradient, ButtonGradientSmall } from '@/components/Buttons';
import { FormValidationError } from '@/components/FormValidationError';
import ReactJson from 'react-json-view';
import Image from 'next/image';

// Types for schema structure
interface SchemaAttribute {
    id: string;
    name: string;
    title: string;
    dataType: string;
    description?: string;
    format?: string;
    required?: boolean;
}

interface SchemaVersion {
    name: string;
    version: string;
    description: string;
    attributes: SchemaAttribute[];
}

// Validation schema
const createSchemaValidation = z.object({
    name: z.string().min(1, 'Schema name is required'),
    version: z.string().min(1, 'Version is required'),
    description: z.string().min(1, 'Description is required'),
});

type SchemaFormData = z.infer<typeof createSchemaValidation>;

// Data type options
const DATA_TYPE_OPTIONS = [
    { value: 'string', label: 'String' },
    { value: 'number', label: 'Number' },
    { value: 'boolean', label: 'Boolean' },
    { value: 'date', label: 'Date' },
    { value: 'uri', label: 'URI' },
];

const IssuerAdmin = () => {
    const [schemaVersion, setSchemaVersion] = useState<SchemaVersion>({
        name: '',
        version: '1.0',
        description: '',
        attributes: [],
    });

    const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['root']));
    const [editingAttribute, setEditingAttribute] = useState<string | null>(null);

    const {
        register,
        handleSubmit,
        formState: { errors, isValid },
        watch,
    } = useForm<SchemaFormData>({
        resolver: zodResolver(createSchemaValidation),
        defaultValues: {
            name: '',
            version: '1.0',
            description: '',
        },
    });

    const watchedValues = watch();

    // Update schema version when form values change
    useEffect(() => {
        setSchemaVersion(prev => ({
            ...prev,
            name: watchedValues.name || '',
            version: watchedValues.version || '1.0',
            description: watchedValues.description || '',
        }));
    }, [watchedValues]);

    // Generate JSON Schema
    const generateJsonSchema = () => {
        const properties: Record<string, any> = {};
        const required: string[] = [];

        schemaVersion.attributes.forEach(attr => {
            properties[attr.name] = {
                type: attr.dataType,
                title: attr.title,
                description: attr.description || `Stores the ${attr.title.toLowerCase()} of the credential subject`,
            };

            if (attr.format) {
                properties[attr.name].format = attr.format;
            }

            if (attr.required) {
                required.push(attr.name);
            }
        });

        return {
            $metadata: {
                uris: {
                    jsonLdContext: 'https://example.com/path/to/file/context.jsonld',
                },
                version: schemaVersion.version,
                schema: 'https://json-schema.org/draft/2020-12/schema',
                properties: {
                    credentialSubject: {
                        description: `Stores the data of the ${schemaVersion.description.toLowerCase()}`,
                        title: `${schemaVersion.name} subject`,
                        properties,
                        required,
                        type: 'object',
                    },
                },
                required: [],
                type: 'object',
            },
            '@context': {
                type: ['string', 'array'],
                '@id': '@type',
            },
        };
    };

    const addAttribute = () => {
        const newAttribute: SchemaAttribute = {
            id: `attr_${Date.now()}`,
            name: `attribute_${schemaVersion.attributes.length + 1}`,
            title: `Attribute ${schemaVersion.attributes.length + 1}`,
            dataType: 'string',
            description: '',
            required: false,
        };

        setSchemaVersion(prev => ({
            ...prev,
            attributes: [...prev.attributes, newAttribute],
        }));
        setEditingAttribute(newAttribute.id);
    };

    const removeAttribute = (id: string) => {
        setSchemaVersion(prev => ({
            ...prev,
            attributes: prev.attributes.filter(attr => attr.id !== id),
        }));
        if (editingAttribute === id) {
            setEditingAttribute(null);
        }
    };

    const updateAttribute = (id: string, updates: Partial<SchemaAttribute>) => {
        setSchemaVersion(prev => ({
            ...prev,
            attributes: prev.attributes.map(attr => (attr.id === id ? { ...attr, ...updates } : attr)),
        }));
    };

    const toggleNode = (nodeId: string) => {
        setExpandedNodes(prev => {
            const newSet = new Set(prev);
            if (newSet.has(nodeId)) {
                newSet.delete(nodeId);
            } else {
                newSet.add(nodeId);
            }
            return newSet;
        });
    };

    const onSubmit = (_data: SchemaFormData) => {
        const jsonSchema = generateJsonSchema();
        console.log('Schema to publish:', jsonSchema);
        // Here you would typically send the schema to your API
    };

    return (
        <div className="flex flex-row items-start gap-4 justify-center h-full px-4 md:px-8 py-4">
            <div className="flex-1 flex flex-col h-full gap-4">
                {/* Details of Issuer */}
                <ItemBox>
                    <div className="p-4 flex flex-col gap-4">
                        <h2 className="text-lg font-semibold text-main-600 border-b border-main-800/20 pb-2">
                            Details of Issuer
                        </h2>
                        <div className="text-sm text-main-800">
                            <p>Configure your schema details and metadata</p>
                        </div>
                    </div>
                </ItemBox>

                {/* Schema Versions */}
                <ItemBox>
                    <div className="p-4 flex flex-col gap-4 h-full">
                        <div className="flex justify-between items-center border-b border-main-800/20 pb-2">
                            <h2 className="text-lg font-semibold text-main-600">Schema Versions</h2>
                            <div className="flex items-center gap-2 text-main-300">
                                <div className="w-3 h-3 bg-main-300 rounded-full"></div>
                                <span className="text-sm">A</span>
                            </div>
                        </div>

                        {/* Tree Structure */}
                        <div className="flex-1 overflow-auto">
                            <div className="space-y-1">
                                {/* Root Schema Node */}
                                <div className="flex items-center gap-2 p-2 hover:bg-main-900/30 rounded">
                                    <button
                                        onClick={() => toggleNode('root')}
                                        className="text-main-600 hover:text-main-100"
                                    >
                                        {expandedNodes.has('root') ? (
                                            <Image
                                                src="/icons/arrow.svg"
                                                alt="collapse"
                                                width={16}
                                                height={16}
                                                className="rotate-180"
                                            />
                                        ) : (
                                            <Image
                                                src="/icons/arrow.svg"
                                                alt="expand"
                                                width={16}
                                                height={16}
                                                className="-rotate-90"
                                            />
                                        )}
                                    </button>
                                    <span className="text-main-600 font-medium">
                                        {schemaVersion.name || 'Schema_Vol_1'}
                                    </span>
                                </div>

                                {/* Meta Data */}
                                {expandedNodes.has('root') && (
                                    <div className="ml-6 space-y-1">
                                        <div className="flex items-center gap-2 p-1 text-sm">
                                            <span className="text-main-800">Meta Data</span>
                                        </div>

                                        {/* Attributes */}
                                        <div className="flex items-center gap-2 p-1">
                                            <button
                                                onClick={() => toggleNode('attributes')}
                                                className="text-main-600 hover:text-main-100"
                                            >
                                                {expandedNodes.has('attributes') ? (
                                                    <Image
                                                        src="/icons/arrow.svg"
                                                        alt="collapse"
                                                        width={16}
                                                        height={16}
                                                        className="rotate-180"
                                                    />
                                                ) : (
                                                    <Image
                                                        src="/icons/arrow.svg"
                                                        alt="expand"
                                                        width={16}
                                                        height={16}
                                                        className="-rotate-90"
                                                    />
                                                )}
                                            </button>
                                            <span className="text-main-100 text-sm">Attribute 1</span>
                                        </div>

                                        {/* Attribute List */}
                                        {expandedNodes.has('attributes') && (
                                            <div className="ml-6 space-y-2">
                                                {schemaVersion.attributes.map(attr => (
                                                    <div
                                                        key={attr.id}
                                                        className="border border-main-800/30 rounded p-3 bg-main-1000/30"
                                                    >
                                                        {editingAttribute === attr.id ? (
                                                            <div className="space-y-3">
                                                                <div className="grid grid-cols-2 gap-2">
                                                                    <input
                                                                        type="text"
                                                                        value={attr.name}
                                                                        onChange={e =>
                                                                            updateAttribute(attr.id, {
                                                                                name: e.target.value,
                                                                            })
                                                                        }
                                                                        placeholder="Attribute name"
                                                                        className="px-2 py-1 text-sm bg-main-900 border border-main-800 rounded text-main-600"
                                                                    />
                                                                    <input
                                                                        type="text"
                                                                        value={attr.title}
                                                                        onChange={e =>
                                                                            updateAttribute(attr.id, {
                                                                                title: e.target.value,
                                                                            })
                                                                        }
                                                                        placeholder="Title"
                                                                        className="px-2 py-1 text-sm bg-main-900 border border-main-800 rounded text-main-600"
                                                                    />
                                                                </div>

                                                                <select
                                                                    value={attr.dataType}
                                                                    onChange={e =>
                                                                        updateAttribute(attr.id, {
                                                                            dataType: e.target.value,
                                                                        })
                                                                    }
                                                                    className="w-full px-2 py-1 text-sm bg-main-900 border border-main-800 rounded text-main-600"
                                                                >
                                                                    {DATA_TYPE_OPTIONS.map(option => (
                                                                        <option key={option.value} value={option.value}>
                                                                            {option.label}
                                                                        </option>
                                                                    ))}
                                                                </select>

                                                                <textarea
                                                                    value={attr.description || ''}
                                                                    onChange={e =>
                                                                        updateAttribute(attr.id, {
                                                                            description: e.target.value,
                                                                        })
                                                                    }
                                                                    placeholder="Description"
                                                                    rows={2}
                                                                    className="w-full px-2 py-1 text-sm bg-main-900 border border-main-800 rounded text-main-600 resize-none"
                                                                />

                                                                <div className="flex items-center gap-2">
                                                                    <input
                                                                        type="checkbox"
                                                                        checked={attr.required || false}
                                                                        onChange={e =>
                                                                            updateAttribute(attr.id, {
                                                                                required: e.target.checked,
                                                                            })
                                                                        }
                                                                        className="w-4 h-4"
                                                                    />
                                                                    <span className="text-sm text-main-600">
                                                                        Required
                                                                    </span>
                                                                </div>

                                                                <div className="flex gap-2">
                                                                    <ButtonGradientSmall
                                                                        onClick={() => setEditingAttribute(null)}
                                                                        type="button"
                                                                    >
                                                                        Save
                                                                    </ButtonGradientSmall>
                                                                    <button
                                                                        onClick={() => removeAttribute(attr.id)}
                                                                        className="px-3 py-1 text-sm bg-red-600 hover:bg-red-700 text-white rounded"
                                                                        type="button"
                                                                    >
                                                                        Remove
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            <div className="flex justify-between items-start">
                                                                <div className="flex-1">
                                                                    <div className="text-sm font-medium text-main-600">
                                                                        {attr.title}
                                                                    </div>
                                                                    <div className="text-xs text-main-800">
                                                                        {attr.name} ({attr.dataType})
                                                                        {attr.required && (
                                                                            <span className="text-red-400 ml-1">*</span>
                                                                        )}
                                                                    </div>
                                                                    {attr.description && (
                                                                        <div className="text-xs text-main-800 mt-1">
                                                                            {attr.description}
                                                                        </div>
                                                                    )}
                                                                </div>
                                                                <div className="flex gap-1">
                                                                    <button
                                                                        onClick={() => setEditingAttribute(attr.id)}
                                                                        className="text-main-100 hover:text-main-600 text-xs px-2 py-1 border border-main-800 rounded"
                                                                    >
                                                                        Edit
                                                                    </button>
                                                                    <button
                                                                        onClick={() => removeAttribute(attr.id)}
                                                                        className="text-red-400 hover:text-red-600 text-xs px-2 py-1 border border-red-800 rounded"
                                                                    >
                                                                        Remove
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                ))}

                                                <button
                                                    onClick={addAttribute}
                                                    className="w-full p-2 border-2 border-dashed border-main-800 rounded text-main-600 hover:border-main-600 hover:text-main-100 flex items-center justify-center gap-2"
                                                >
                                                    <span className="text-lg">+</span>
                                                    Add next attribute
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="border-t border-main-800/20 pt-4 space-y-2">
                            <button
                                onClick={() => setExpandedNodes(new Set(['root', 'attributes']))}
                                className="w-full py-2 text-sm bg-main-900 hover:bg-main-800 text-main-600 rounded"
                            >
                                Save
                            </button>
                            <ButtonGradient
                                onClick={handleSubmit(onSubmit)}
                                disabled={!isValid || schemaVersion.attributes.length === 0}
                            >
                                Publish
                            </ButtonGradient>
                        </div>
                    </div>
                </ItemBox>
            </div>

            <div className="flex-1 flex flex-col h-full gap-4">
                {/* Schema Display - Tree View */}
                <ItemBox>
                    <div className="p-4 flex flex-col gap-4 flex-[2]">
                        <h2 className="text-lg font-semibold text-main-600 border-b border-main-800/20 pb-2">
                            Schema Versions
                        </h2>

                        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="flex flex-col gap-2">
                                    <TextInput
                                        {...register('name')}
                                        label="Schema Name *"
                                        placeholder="Schema_Vol_1"
                                        labelNode={null}
                                    />
                                    <FormValidationError message={errors.name?.message} />
                                </div>

                                <div className="flex flex-col gap-2">
                                    <TextInput
                                        {...register('version')}
                                        label="Version *"
                                        placeholder="1.0"
                                        labelNode={null}
                                    />
                                    <FormValidationError message={errors.version?.message} />
                                </div>
                            </div>

                            <div className="flex flex-col gap-2">
                                <TextInput
                                    {...register('description')}
                                    label="Description *"
                                    placeholder="Schema description"
                                    labelNode={null}
                                />
                                <FormValidationError message={errors.description?.message} />
                            </div>
                        </form>
                    </div>
                </ItemBox>

                {/* JSON Preview */}
                <ItemBox>
                    <div className="p-4 flex flex-col gap-4 h-full">
                        <div className="flex justify-between items-center border-b border-main-800/20 pb-2">
                            <h2 className="text-lg font-semibold text-main-600">JSON Preview</h2>
                            <button className="text-main-600 hover:text-main-100">
                                <Image
                                    src="/icons/arrow.svg"
                                    alt="collapse"
                                    width={20}
                                    height={20}
                                    className="rotate-180"
                                />
                            </button>
                        </div>

                        <div className="flex-1 overflow-auto">
                            <div className="text-sm">
                                <div className="mb-2 text-main-800">Preview</div>
                                <div className="mb-2 text-main-800">JSON Schema</div>
                                <div className="bg-main-1000/30 rounded p-3 font-mono text-xs">
                                    <ReactJson
                                        src={generateJsonSchema()}
                                        theme="twilight"
                                        style={{ background: 'transparent' }}
                                        displayDataTypes={false}
                                        collapsed={2}
                                        name={false}
                                        enableClipboard={true}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </ItemBox>
            </div>
        </div>
    );
};

export default IssuerAdmin;
